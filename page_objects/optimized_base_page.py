"""
优化后的基础页面类
采用混入模式，将功能模块化
"""
import os
from typing import Literal
from playwright.sync_api import Page

from constants import DEFAULT_TIMEOUT
from utils.logger import logger
from utils.variable_manager import VariableManager

# 导入混入类（需要先创建这些文件）
# from .mixins.element_operations import ElementOperationsMixin
# from .mixins.assertion_operations import AssertionOperationsMixin
# from .mixins.wait_operations import WaitOperationsMixin
# from .mixins.navigation_operations import NavigationOperationsMixin
# from .mixins.keyboard_operations import KeyboardOperationsMixin
# from .mixins.file_operations import FileOperationsMixin
# from .mixins.network_monitoring import NetworkMonitoringMixin


def base_url():
    return os.environ.get("BASE_URL")


class OptimizedBasePage:
    """
    优化后的基础页面类
    使用混入模式组织功能模块
    """
    
    def __init__(self, page: Page):
        self.page = page
        self.pages = [self.page]
        self.variable_manager = VariableManager()
        self._setup_page_handlers()

    def _setup_page_handlers(self):
        """设置页面事件处理器"""
        def handle_page_error(exc):
            error_msg = str(exc)
            # 过滤掉一些不重要的错误
            if "The play() request was interrupted" in error_msg:
                logger.debug(f"页面媒体播放中断（可忽略）: {error_msg}")
            else:
                logger.error(f"页面错误: {exc}")

        self.page.on("pageerror", handle_page_error)
        self.page.on("crash", lambda: logger.error("页面崩溃"))

    def _locator(
        self,
        selector: str,
        state: Literal["attached", "detached", "hidden", "visible"] = "visible",
        timeout: int = DEFAULT_TIMEOUT,
    ):
        """统一的元素定位与等待方法"""
        try:
            # 首先等待元素达到指定状态
            self.page.wait_for_selector(selector, state=state, timeout=timeout)
            # 然后返回定位器
            return self.page.locator(selector)
        except Exception as e:
            error_msg = str(e)
            if "Timeout" in error_msg:
                logger.warning(f"元素定位超时 {selector} (state={state}, timeout={timeout}ms): 元素可能不存在或状态不匹配")
            else:
                logger.error(f"定位元素 {selector} 失败 (state={state}): {error_msg}")
            raise Exception(f"定位或等待元素 {selector} 失败 (state={state}): {error_msg}")

    # 基础操作方法示例（实际应该从混入类继承）
    def click(self, selector: str):
        """点击元素"""
        self._locator(selector).first.click(timeout=DEFAULT_TIMEOUT)

    def fill(self, selector: str, value):
        """填写文本"""
        resolved_text = self.variable_manager.replace_variables_refactored(value)
        if resolved_text is not None:
            resolved_text = str(resolved_text)
        self._locator(selector).fill(resolved_text)

    def get_text(self, selector: str) -> str:
        """获取元素文本"""
        return self._locator(selector).first.inner_text()

    def store_variable(self, name: str, value: str, scope: str = "global"):
        """存储变量"""
        self.variable_manager.set_variable(name, value, scope)

    def get_element_count(self, selector: str) -> int:
        """获取元素数量"""
        locator = self._locator(selector, state="attached")
        return locator.count()


# 如果要使用混入模式，可以这样定义：
# class OptimizedBasePage(
#     ElementOperationsMixin,
#     AssertionOperationsMixin, 
#     WaitOperationsMixin,
#     NavigationOperationsMixin,
#     KeyboardOperationsMixin,
#     FileOperationsMixin,
#     NetworkMonitoringMixin
# ):
#     """优化后的基础页面类，使用多重继承组合各种功能"""
#     
#     def __init__(self, page: Page):
#         self.page = page
#         self.pages = [self.page]
#         self.variable_manager = VariableManager()
#         self._setup_page_handlers()
#         
#         # 初始化所有混入类
#         super().__init__()
