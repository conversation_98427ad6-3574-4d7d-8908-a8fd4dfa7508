# BasePage 性能优化建议

## 1. 元素定位优化

### 问题
- 每次调用 `_locator` 都会执行 `wait_for_selector`，造成不必要的等待
- 重复的元素定位操作

### 优化方案
```python
class OptimizedBasePage:
    def __init__(self, page: Page):
        self.page = page
        self._locator_cache = {}  # 元素定位缓存
        
    def _locator(self, selector: str, state="visible", timeout=DEFAULT_TIMEOUT, use_cache=True):
        """优化的元素定位方法"""
        cache_key = f"{selector}_{state}_{timeout}"
        
        # 使用缓存（适用于静态元素）
        if use_cache and cache_key in self._locator_cache:
            return self._locator_cache[cache_key]
            
        try:
            # 智能等待：先检查元素是否已存在
            if state == "visible":
                locator = self.page.locator(selector)
                if locator.count() > 0 and locator.first.is_visible():
                    if use_cache:
                        self._locator_cache[cache_key] = locator
                    return locator
            
            # 如果元素不存在或不可见，才执行等待
            self.page.wait_for_selector(selector, state=state, timeout=timeout)
            locator = self.page.locator(selector)
            
            if use_cache:
                self._locator_cache[cache_key] = locator
            return locator
            
        except Exception as e:
            # 清理缓存中的失效项
            if cache_key in self._locator_cache:
                del self._locator_cache[cache_key]
            raise
    
    def clear_locator_cache(self):
        """清理元素定位缓存"""
        self._locator_cache.clear()
```

## 2. 截图优化

### 问题
- 每次断言失败都截图，影响性能
- 截图文件过大

### 优化方案
```python
class ScreenshotOptimizer:
    def __init__(self):
        self.screenshot_count = 0
        self.max_screenshots = 10  # 限制截图数量
        
    def smart_screenshot(self, page, name="screenshot", force=False):
        """智能截图：只在必要时截图"""
        if not force and self.screenshot_count >= self.max_screenshots:
            logger.debug("已达到最大截图数量限制，跳过截图")
            return
            
        try:
            # 压缩截图
            screenshot = page.screenshot(
                quality=60,  # 降低质量以减小文件大小
                type='jpeg'  # 使用JPEG格式
            )
            allure.attach(screenshot, name=name, attachment_type=allure.attachment_type.JPG)
            self.screenshot_count += 1
        except Exception as e:
            logger.warning(f"截图失败: {e}")
```

## 3. 日志优化

### 问题
- 过多的调试日志影响性能
- 日志信息重复

### 优化方案
```python
class LogOptimizer:
    def __init__(self):
        self.log_cache = set()  # 避免重复日志
        
    def smart_log(self, level, message, dedupe=True):
        """智能日志：避免重复信息"""
        if dedupe and message in self.log_cache:
            return
            
        if dedupe:
            self.log_cache.add(message)
            
        getattr(logger, level)(message)
        
        # 限制缓存大小
        if len(self.log_cache) > 1000:
            self.log_cache.clear()
```

## 4. 等待策略优化

### 问题
- 固定超时时间不够灵活
- 不必要的等待

### 优化方案
```python
class SmartWait:
    def __init__(self, page):
        self.page = page
        
    def adaptive_wait(self, selector, max_timeout=DEFAULT_TIMEOUT):
        """自适应等待：根据网络状况调整超时时间"""
        # 检测网络延迟
        start_time = time.time()
        self.page.evaluate("() => performance.now()")
        network_delay = (time.time() - start_time) * 1000
        
        # 根据网络延迟调整超时时间
        if network_delay > 100:
            timeout = max_timeout * 1.5
        elif network_delay < 50:
            timeout = max_timeout * 0.8
        else:
            timeout = max_timeout
            
        return min(timeout, max_timeout * 2)  # 设置上限
```

## 5. 内存优化

### 问题
- 页面对象可能存在内存泄漏
- 变量管理器占用过多内存

### 优化方案
```python
class MemoryOptimizer:
    def __init__(self):
        self.cleanup_threshold = 100  # 清理阈值
        self.operation_count = 0
        
    def auto_cleanup(self):
        """自动清理：定期清理缓存和临时数据"""
        self.operation_count += 1
        
        if self.operation_count % self.cleanup_threshold == 0:
            # 清理元素定位缓存
            if hasattr(self, '_locator_cache'):
                self._locator_cache.clear()
                
            # 清理变量管理器中的临时变量
            if hasattr(self, 'variable_manager'):
                self.variable_manager.cleanup_temp_variables()
                
            # 强制垃圾回收
            import gc
            gc.collect()
            
            logger.debug("执行内存清理")
```
