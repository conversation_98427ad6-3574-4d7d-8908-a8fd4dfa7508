# BasePage 代码质量修复建议

## 1. 逻辑错误修复

### 问题：switch_window 方法逻辑错误
**位置：** 第602-608行
**问题：** 在成功切换窗口后仍然抛出异常

```python
# 当前代码（有问题）
@handle_page_error(description="切换窗口")
def switch_window(self, value=0):
    """切换到指定窗口"""
    if value < 0 or value >= len(self.pages):
        raise ValueError("无效的窗口索引")
    """切换到指定窗口"""
    self.page = self.pages[value]
    raise ValueError("未找到匹配的窗口")  # 这行代码永远会执行

# 修复后的代码
@handle_page_error(description="切换窗口")
def switch_window(self, value=0):
    """切换到指定窗口"""
    if value < 0 or value >= len(self.pages):
        raise ValueError("无效的窗口索引")
    
    self.page = self.pages[value]
    logger.info(f"成功切换到窗口索引: {value}")
```

## 2. 重复代码优化

### 问题：重复的元素定位调用
**位置：** 第669-670行

```python
# 当前代码（重复调用）
@handle_page_error(description="获取元素属性")
def get_element_attribute(self, selector: str, attribute: str) -> str:
    """获取元素属性"""
    self._locator(selector, state="attached")  # 第一次调用
    return self._locator(selector).get_attribute(attribute)  # 第二次调用

# 优化后的代码
@handle_page_error(description="获取元素属性")
def get_element_attribute(self, selector: str, attribute: str) -> str:
    """获取元素属性"""
    locator = self._locator(selector, state="attached")
    return locator.get_attribute(attribute)
```

## 3. 异常处理优化

### 问题：JSONPath 处理可能出现索引错误
**位置：** 第1029行和第1048行

```python
# 当前代码（可能出现索引错误）
matches = [value.value for value in expr.find(data)][0]

# 优化后的代码
def _get_jsonpath_value(self, data, jsonpath_expr):
    """安全获取JSONPath值"""
    expr = parse(jsonpath_expr.strip())
    matches = [value.value for value in expr.find(data)]
    
    if not matches:
        raise ValueError(f"JSONPath {jsonpath_expr} 未找到匹配项，当前数据: {data}")
    
    return matches[0] if len(matches) == 1 else matches
```

## 4. 代码清理建议

### 删除注释代码
**位置：** 多处存在注释掉的代码

```python
# 删除这些注释代码
# actual_values = self.page.locator(selector).evaluate(
#     "el => Array.from(el.selectedOptions).map(o => o.value)"
# )
# allure.attach(
#     f"断言成功: 元素 {selector} 的值\n期望: {resolved_values}\n实际: {actual_values}",
#     name="断言结果",
#     attachment_type=allure.attachment_type.TEXT,
# )
```

## 5. 类型注解完善

### 问题：部分方法缺少完整的类型注解

```python
# 优化前
def _locator(self, selector: str, state="visible", timeout=DEFAULT_TIMEOUT):

# 优化后
def _locator(
    self, 
    selector: str, 
    state: Literal["attached", "detached", "hidden", "visible"] = "visible",
    timeout: int = DEFAULT_TIMEOUT
) -> Locator:
```

## 6. 方法职责优化

### 问题：monitor_action_request 和 monitor_action_response 方法过长

```python
# 建议拆分为更小的方法
class NetworkMonitor:
    def _execute_action(self, action: str, selector: str, **kwargs):
        """执行指定的操作"""
        action_map = {
            "click": lambda: self.click(selector),
            "fill": lambda: self.fill(selector, kwargs.get("value")),
            "press_key": lambda: self.press_key(selector, kwargs.get("key")),
            "select": lambda: self.select_option(selector, kwargs.get("value")),
            "goto": lambda: self.navigate(kwargs.get("value"))
        }
        
        if action in action_map:
            action_map[action]()
        else:
            logger.warning(f"不支持的操作类型: {action}，将执行默认点击操作")
            self.click(selector)
    
    def _process_request_data(self, request):
        """处理请求数据"""
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                return request.post_data_json()
            except Exception:
                return json.loads(request.post_data)
        else:
            # 处理GET请求参数
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(request.url)
            request_data = parse_qs(parsed_url.query)
            
            # 将单项列表值转换为单个值
            for key, value in request_data.items():
                if isinstance(value, list) and len(value) == 1:
                    request_data[key] = value[0]
            
            return request_data
```

## 7. 常量提取

### 问题：硬编码的魔法数字

```python
# 在 constants.py 中添加
DEFAULT_SLEEP_INTERVAL = 100  # 毫秒
MAX_SCREENSHOT_COUNT = 10
SCREENSHOT_QUALITY = 60

# 在代码中使用常量
self.page.wait_for_timeout(DEFAULT_SLEEP_INTERVAL)
```

## 8. 文档字符串改进

### 问题：部分方法的文档字符串不够详细

```python
def monitor_action_request(
    self,
    url_pattern: str,
    selector: str,
    action: str = "click",
    assert_params: Dict[str, Any] = None,
    timeout: int = DEFAULT_TIMEOUT,
    **kwargs,
) -> Dict[str, Any]:
    """
    监测操作触发的请求并验证参数
    
    Args:
        url_pattern: URL匹配模式，支持通配符，如 "**/api/user/**"
        selector: 要操作的元素选择器
        action: 要执行的操作类型，支持 "click", "fill", "press_key", "select", "goto"
        assert_params: 要验证的参数字典，格式为 {"$.path.to.field": expected_value}
        timeout: 等待超时时间，单位毫秒，默认使用 DEFAULT_TIMEOUT
        **kwargs: 其他操作参数，如 fill 操作的 value 参数
    
    Returns:
        Dict[str, Any]: 包含请求信息的字典，包含 url, method, data, headers 字段
    
    Raises:
        TimeoutError: 当在指定时间内未捕获到匹配的请求时
        ValueError: 当 JSONPath 验证失败时
        
    Example:
        # 监测点击按钮触发的API请求
        request_data = page.monitor_action_request(
            url_pattern="**/api/submit",
            selector="#submit-btn",
            action="click",
            assert_params={"$.status": "success"}
        )
    """
```
